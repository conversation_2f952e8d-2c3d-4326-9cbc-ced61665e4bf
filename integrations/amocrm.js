import IntegrationInterface from './base.js';
import axios from 'axios';
import {log} from '../library/log.js';

/**
 * AmoCRM Integration
 */
class AmoCRMIntegration extends IntegrationInterface {
    constructor(config) {
        super(config);
        this.domain = config.domain;
        this.accessToken = config.access_token;
        this.refreshToken = config.refresh_token;
        this.clientId = config.client_id;
        this.clientSecret = config.client_secret;
    }

    /**
     * Send data to AmoCRM (create lead)
     */
    async send(data) {
        try {
            log.info('Sending data to AmoCRM', {data}, 'send', 'AmoCRMIntegration');

            await this.ensureValidToken();

            const leadData = this.formatDataForAmoCRM(data);
            const response = await this.makeRequest('leads', 'POST', [leadData]);

            if (response._embedded && response._embedded.leads && response._embedded.leads[0]) {
                const lead = response._embedded.leads[0];
                return {
                    success: true,
                    externalId: lead.id.toString(),
                    data: response
                };
            } else {
                throw new Error('Failed to create lead in AmoCRM');
            }
        } catch (error) {
            log.error('Error sending data to AmoCRM', {
                error: error.message,
                data
            }, 'send', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Update data in AmoCRM
     */
    async update(externalId, data) {
        try {
            log.info('Updating data in AmoCRM', {externalId, data}, 'update', 'AmoCRMIntegration');

            await this.ensureValidToken();

            const leadData = this.formatDataForAmoCRM(data);
            leadData.id = parseInt(externalId);

            const response = await this.makeRequest('leads', 'PATCH', [leadData]);

            return {
                success: !!response._embedded?.leads?.[0],
                data: response
            };
        } catch (error) {
            log.error('Error updating data in AmoCRM', {
                error: error.message,
                externalId,
                data
            }, 'update', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Delete data from AmoCRM
     */
    async delete(externalId) {
        try {
            log.info('Deleting data from AmoCRM', {externalId}, 'delete', 'AmoCRMIntegration');

            await this.ensureValidToken();

            const response = await this.makeRequest(`leads/${externalId}`, 'DELETE');

            return {
                success: response.status === 204 || response.status === 200,
                data: response
            };
        } catch (error) {
            log.error('Error deleting data from AmoCRM', {
                error: error.message,
                externalId
            }, 'delete', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Find data in AmoCRM
     */
    async find(query) {
        try {
            log.info('Finding data in AmoCRM', {query}, 'find', 'AmoCRMIntegration');

            await this.ensureValidToken();

            const params = new URLSearchParams();
            if (query.name) params.append('query', query.name);
            if (query.email) params.append('query', query.email);
            if (query.phone) params.append('query', query.phone);

            const response = await this.makeRequest(`leads?${params.toString()}`, 'GET');

            return {
                success: true,
                data: response._embedded?.leads || []
            };
        } catch (error) {
            log.error('Error finding data in AmoCRM', {
                error: error.message,
                query
            }, 'find', 'AmoCRMIntegration');
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Test connection to AmoCRM
     */
    async testConnection() {
        try {
            await this.ensureValidToken();
            const response = await this.makeRequest('leads?limit=1', 'GET');
            return !!response;
        } catch (error) {
            log.error('AmoCRM connection test failed', {
                error: error.message
            }, 'testConnection', 'AmoCRMIntegration');
            return false;
        }
    }

    /**
     * Get configuration schema
     */
    getConfigSchema() {
        return {
            domain: {
                type: 'string',
                required: true,
                description: 'AmoCRM domain (e.g., your-domain.amocrm.ru)'
            },
            access_token: {
                type: 'string',
                required: true,
                description: 'Access token'
            },
            refresh_token: {
                type: 'string',
                required: true,
                description: 'Refresh token'
            },
            client_id: {
                type: 'string',
                required: true,
                description: 'Client ID'
            },
            client_secret: {
                type: 'string',
                required: true,
                description: 'Client Secret'
            }
        };
    }

    /**
     * Validate configuration
     */
    validateConfig(config) {
        const errors = [];
        const required = ['domain', 'access_token', 'refresh_token', 'client_id', 'client_secret'];

        required.forEach(field => {
            if (!config[field]) {
                errors.push(`${field} is required`);
            }
        });

        if (config.domain && !config.domain.includes('.amocrm.')) {
            errors.push('Invalid AmoCRM domain format');
        }

        return {
            valid: errors.length === 0,
            errors
        };
    }

    /**
     * Ensure access token is valid, refresh if needed
     */
    async ensureValidToken() {
        // For simplicity, we'll assume the token is valid
        // In a real implementation, you would check token expiry and refresh if needed
        return true;
    }

    /**
     * Make API request to AmoCRM
     */
    async makeRequest(endpoint, method = 'GET', data = null) {
        const url = `https://${this.domain}/api/v4/${endpoint}`;

        const config = {
            method,
            url,
            headers: {
                'Authorization': `Bearer ${this.accessToken}`,
                'Content-Type': 'application/json'
            },
            timeout: 30000
        };

        if (data && (method === 'POST' || method === 'PATCH')) {
            config.data = data;
        }

        const response = await axios(config);
        return response.data;
    }

    /**
     * Format data for AmoCRM lead
     */
    formatDataForAmoCRM(data) {
        const leadData = {
            name: data.title || 'Dialog Analysis Result',
            price: 0,
            custom_fields_values: []
        };

        // Add analysis as a note/comment
        if (data.analysis || data.content) {
            leadData.custom_fields_values.push({
                field_code: 'COMMENTS',
                values: [{
                    value: data.analysis || data.content
                }]
            });
        }

        // Add contact information if available
        if (data.contact) {
            if (data.contact.email) {
                leadData.custom_fields_values.push({
                    field_code: 'EMAIL',
                    values: [{
                        value: data.contact.email,
                        enum_code: 'WORK'
                    }]
                });
            }
            if (data.contact.phone) {
                leadData.custom_fields_values.push({
                    field_code: 'PHONE',
                    values: [{
                        value: data.contact.phone,
                        enum_code: 'WORK'
                    }]
                });
            }
        }

        return leadData;
    }
}

export default AmoCRMIntegration;

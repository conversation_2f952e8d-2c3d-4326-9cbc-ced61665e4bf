import {ToolInterface} from './base.js';
import {log} from '../library/log.js';
import {l, lp} from '../library/translation.js';
import {Dialogue, DialogueMessage, ExternalUser, DialogueAnalysis, BotModels} from '../models/associations.js';
import {recordTokenUsage} from '../utils/tokens.js';
import {getStorage} from '../library/storage.js';
import {Op} from 'sequelize';
import fs from 'fs';
import path from 'path';

// Import integrations
import Bitrix24Integration from '../integrations/bitrix24.js';
import AmoCRMIntegration from '../integrations/amocrm.js';
import RestApiIntegration from '../integrations/rest_api.js';

/**
 * Dialog Analyzer Tool - Analyzes completed dialogues and sends results to CRM systems
 */
class DialogAnalyzerTool extends ToolInterface {
    constructor(context) {
        super(context);
        this.integrationClasses = {
            'bitrix24': Bitrix24Integration,
            'amocrm': AmoCRMIntegration,
            'rest_api': RestApiIntegration
        };
    }

    getDefinition() {
        return {}; // No OpenAI tool definition needed
    }

    /**
     * Register for menu callback events
     */
    getRegisteredEvents() {
        return ['menu_callback', 'callback_query', 'message_received'];
    }

    /**
     * Return cron schedule - every 5 minutes
     */
    getCronSettings() {
        return '*/5 * * * *'; // Every 5 minutes
    }

    /**
     * Define menu items for dialog analyzer (owner only)
     */
    getMenuItems() {
        return [
            {
                id: 'd_a_main',
                text: '🔍 Dialog Analyzer',
                callback_data: 'd_a_main',
                position: 'main',
                permissions: ['owner'],
                order: 40
            },
            // Settings submenu
            {
                id: 'd_a_settings',
                text: '⚙️ Settings',
                callback_data: 'd_a_settings',
                position: 'dialog_analyzer',
                permissions: ['owner'],
                order: 10
            },
            {
                id: 'd_a_test',
                text: '🧪 Manual Dialog Analysis',
                callback_data: 'd_a_test',
                position: 'dialog_analyzer',
                permissions: ['owner'],
                order: 20
            },
            {
                id: 'd_a_status',
                text: '📊 Status',
                callback_data: 'd_a_status',
                position: 'dialog_analyzer',
                permissions: ['owner'],
                order: 30
            },
            // Settings submenu items
            {
                id: 'd_a_enable_disable',
                text: '🔄 Enable/Disable Analyzer',
                callback_data: 'd_a_enable_disable',
                position: 'd_a_settings',
                permissions: ['owner'],
                order: 5
            },
            {
                id: 'd_a_prompt',
                text: '📝 Analysis Prompt',
                callback_data: 'd_a_prompt',
                position: 'd_a_settings',
                permissions: ['owner'],
                order: 10
            },
            {
                id: 'd_a_model',
                text: '🤖 AI Model',
                callback_data: 'd_a_model',
                position: 'd_a_settings',
                permissions: ['owner'],
                order: 20
            },
            {
                id: 'd_a_timeout',
                text: '⏱️ Inactivity Timeout',
                callback_data: 'd_a_timeout',
                position: 'd_a_settings',
                permissions: ['owner'],
                order: 30
            },
            {
                id: 'd_a_reanalysis',
                text: '🔄 Re-analysis Settings',
                callback_data: 'd_a_reanalysis',
                position: 'd_a_settings',
                permissions: ['owner'],
                order: 40
            },
            {
                id: 'd_a_integration',
                text: '🔗 CRM Integration',
                callback_data: 'd_a_integration',
                position: 'd_a_settings',
                permissions: ['owner'],
                order: 50
            }
        ];
    }

    /**
     * Handle events
     */
    async handleEvent(eventName, eventData, context) {
        if (eventName === 'menu_callback') {
            const {menuItem} = eventData;
            const callbackData = menuItem.callback_data;

            switch (callbackData) {
                case 'd_a_main':
                    return await this.showMainMenu(context);
                case 'd_a_settings':
                    return await this.showSettingsMenu(context);
                case 'd_a_test':
                    return await this.showTestMenu(context);
                case 'd_a_status':
                    return await this.showStatus(context);
                case 'd_a_enable_disable':
                    return await this.toggleAnalyzer(context);
                case 'd_a_prompt':
                    return await this.configurePrompt(context);
                case 'd_a_model':
                    return await this.configureModel(context);
                case 'd_a_timeout':
                    return await this.configureTimeout(context);
                case 'd_a_reanalysis':
                    return await this.configureReanalysis(context);
                case 'd_a_integration':
                    return await this.configureIntegration(context);
                default:
                    return false;
            }
        }

        if (eventName === 'callback_query') {
            return await this.handleCallbackQuery(eventData, context);
        }

        if (eventName === 'message_received') {
            return await this.handleMessage(eventData, context);
        }

        return false;
    }

    /**
     * Handle callback queries (inline keyboard buttons)
     */
    async handleCallbackQuery(eventData, context) {
        const callbackData = eventData.data;
        const {client, chatId, user, botStorage} = context;

        if (eventData.message) {
            setTimeout(() => {
                try {
                    client.deleteMessage(eventData.message.chat.id, eventData.message.message_id);
                } catch (e) {
                    log.error('Failed to delete message', {
                        error: e.message,
                        chatId: eventData.message.chat.id,
                        messageId: eventData.message.message_id,
                        botId: context.bot.id
                    }, 'handleCallbackQuery', 'DialogAnalyzerTool');
                }
            }, 2000);

        }

        // Handle model selection
        if (callbackData.startsWith('d_a_set_model_')) {
            const model = callbackData.replace('d_a_set_model_', '');
            const config = await this.getConfig(botStorage);
            config.model = model;
            await botStorage.set('dialogAnalyzer.config', config);

            await client.sendMessage(chatId, await l(user, `AI model set to: ${model}`));
            return true;
        }

        // Handle timeout selection
        if (callbackData.startsWith('d_a_set_timeout_')) {
            const timeoutStr = callbackData.replace('d_a_set_timeout_', '');
            const config = await this.getConfig(botStorage);

            if (timeoutStr === 'disabled') {
                config.inactivityTimeout = -1; // -1 means disabled
                await botStorage.set('dialogAnalyzer.config', config);
                await client.sendMessage(chatId, await l(user, "Automated analysis disabled"));
            } else {
                const timeout = parseInt(timeoutStr);
                config.inactivityTimeout = timeout;
                await botStorage.set('dialogAnalyzer.config', config);

                const minutes = timeout / 60;
                await client.sendMessage(chatId, await l(user, `Inactivity timeout set to: ${minutes} minutes`));
            }
            return true;
        }

        // Handle re-analysis toggle
        if (callbackData === 'd_a_toggle_reanalysis') {
            const config = await this.getConfig(botStorage);
            config.reanalysisEnabled = !config.reanalysisEnabled;
            await botStorage.set('dialogAnalyzer.config', config);

            const status = config.reanalysisEnabled ? 'enabled' : 'disabled';
            await client.sendMessage(chatId, await l(user, `Re-analysis ${status}`));
            return true;
        }

        // Handle dialogue selection for analysis
        if (callbackData.startsWith('d_a_a_d_')) {
            const dialogueId = callbackData.replace('d_a_a_d_', '');
            await this.testAnalyzeDialogue(dialogueId, context, false);
            return true;
        }

        // Handle pagination - previous page
        if (callbackData.startsWith('d_a_prev_page_')) {
            const offset = parseInt(callbackData.replace('d_a_prev_page_', ''));
            await this.showTestMenu(context, offset);
            return true;
        }

        // Handle pagination - next page
        if (callbackData.startsWith('d_a_next_page_')) {
            const offset = parseInt(callbackData.replace('d_a_next_page_', ''));
            await this.showTestMenu(context, offset);
            return true;
        }

        // Handle prompt update confirmation
        if (callbackData === 'd_a_update_prompt_yes') {
            const message = await l(user, "Please send the new analysis prompt for dialogues:");
            await client.sendMessage(chatId, message);

            // Set waiting state
            const userStorage = getStorage('ExternalUser', user.id);
            await userStorage.set('dialogAnalyzer.waitingForPrompt', true);
            return true;
        }

        if (callbackData === 'd_a_update_prompt_no') {
            await client.sendMessage(chatId, await l(user, "Prompt update cancelled"));
            return true;
        }

        // Handle prompt save confirmation
        if (callbackData === 'd_a_save_prompt_yes') {
            const userStorage = getStorage('ExternalUser', user.id);
            const newPrompt = await userStorage.get('dialogAnalyzer.pendingPrompt');

            if (newPrompt) {
                const config = await this.getConfig(botStorage);
                config.prompt = newPrompt;
                await botStorage.set('dialogAnalyzer.config', config);

                await userStorage.del('dialogAnalyzer.pendingPrompt');
                await client.sendMessage(chatId, await l(user, "Analysis prompt updated successfully"));
            } else {
                await client.sendMessage(chatId, await l(user, "Error: No pending prompt found"));
            }
            return true;
        }

        if (callbackData === 'd_a_save_prompt_no') {
            const userStorage = getStorage('ExternalUser', user.id);
            await userStorage.del('dialogAnalyzer.pendingPrompt');
            await client.sendMessage(chatId, await l(user, "Prompt update cancelled"));
            return true;
        }

        // Handle integration selection
        if (callbackData.startsWith('d_a_set_integration_')) {
            const integrationType = callbackData.replace('d_a_set_integration_', '');

            if (integrationType === 'none') {
                const config = await this.getConfig(botStorage);
                config.integrationType = null;
                config.integrationConfig = {};
                await botStorage.set('dialogAnalyzer.config', config);

                await client.sendMessage(chatId, await l(user, "CRM integration disabled"));
                return true;
            } else {
                // Start integration configuration
                await this.startIntegrationConfig(integrationType, context);
                return true;
            }
        }

        return false;
    }

    /**
     * Handle text messages
     */
    async handleMessage(eventData, context) {
        const {text} = eventData;
        const {client, chatId, user, botStorage} = context;
        const userStorage = getStorage('ExternalUser', user.id);

        // Check if waiting for prompt
        const waitingForPrompt = await userStorage.get('dialogAnalyzer.waitingForPrompt', false);
        if (waitingForPrompt) {
            await userStorage.del('dialogAnalyzer.waitingForPrompt');

            // Store the new prompt temporarily and ask for confirmation
            await userStorage.set('dialogAnalyzer.pendingPrompt', text);

            let message = `📝 <b>${await l(user, "Confirm New Prompt")}</b>\n\n`;
            message += await l(user, "New prompt:");
            message += `\n\n<code>${text}</code>\n\n`;
            message += await l(user, "Do you want to save this prompt?");

            const inlineKeyboard = [
                [{
                    text: await l(user, "✅ Yes, Save"),
                    callback_data: 'd_a_save_prompt_yes'
                }],
                [{
                    text: await l(user, "❌ No, Cancel"),
                    callback_data: 'd_a_save_prompt_no'
                }]
            ];

            await client.sendMessage(chatId, message, {
                parse_mode: 'HTML',
                reply_markup: {
                    inline_keyboard: inlineKeyboard
                }
            });
            return true;
        }

        // Check if waiting for integration config
        const waitingForIntegrationConfig = await userStorage.get('dialogAnalyzer.waitingForIntegrationConfig', null);
        if (waitingForIntegrationConfig) {
            await this.handleIntegrationConfigInput(waitingForIntegrationConfig, text, context);
            return true;
        }

        return false;
    }

    /**
     * Cron execution method - analyzes inactive dialogues
     */
    async cron(currentTime, context) {
        try {
            log.info('Running dialog analyzer cron job', {
                botId: context.bot.id,
                time: currentTime.toISOString()
            }, 'cron', 'DialogAnalyzerTool');

            const botStorage = context.botStorage;
            const config = await this.getConfig(botStorage);

            // Skip if analyzer is not configured or automated analysis is disabled
            if (!config.enabled || config.inactivityTimeout === -1) {
                log.debug('Dialog analyzer is disabled or automated analysis is disabled', {
                    botId: context.bot.id,
                    enabled: config.enabled,
                    timeout: config.inactivityTimeout
                }, 'cron', 'DialogAnalyzerTool');
                return {success: true, message: 'Analyzer is disabled or automated analysis is disabled'};
            }

            // Find inactive dialogues
            const inactiveDialogues = await this.findInactiveDialogues(context.bot.id, config.inactivityTimeout);

            if (inactiveDialogues.length === 0) {
                log.debug('No inactive dialogues found', {
                    botId: context.bot.id
                }, 'cron', 'DialogAnalyzerTool');
                return {success: true, analyzed: 0};
            }

            let analyzedCount = 0;
            const errors = [];

            for (const dialogue of inactiveDialogues) {
                try {
                    const result = await this.analyzeDialogue(dialogue, config, context);
                    if (result.success) {
                        analyzedCount++;
                    } else {
                        errors.push(`Dialogue ${dialogue.id}: ${result.error}`);
                    }
                } catch (error) {
                    log.error('Error analyzing dialogue', {
                        dialogueId: dialogue.id,
                        error: error.message
                    }, 'cron', 'DialogAnalyzerTool');
                    errors.push(`Dialogue ${dialogue.id}: ${error.message}`);
                }
            }

            log.info('Dialog analyzer cron completed', {
                botId: context.bot.id,
                totalDialogues: inactiveDialogues.length,
                analyzedCount,
                errorsCount: errors.length
            }, 'cron', 'DialogAnalyzerTool');

            return {
                success: true,
                analyzed: analyzedCount,
                errors: errors.length > 0 ? errors : undefined
            };
        } catch (error) {
            log.error('Error in dialog analyzer cron job', {
                error: error.message,
                botId: context.bot.id
            }, 'cron', 'DialogAnalyzerTool');
            return {success: false, error: error.message};
        }
    }

    /**
     * Show main menu
     */
    async showMainMenu(context) {
        const {client, chatId, user} = context;

        const menuItems = await client.getMenuItems('dialog_analyzer', ['owner']);
        const menuTitle = await l(user, "Dialog Analyzer");

        await client.sendMenu(chatId, menuTitle, menuItems);
        return true;
    }

    /**
     * Show settings menu
     */
    async showSettingsMenu(context) {
        const {client, chatId, user} = context;

        const menuItems = await client.getMenuItems('d_a_settings', ['owner']);
        const menuTitle = await l(user, "Dialog Analyzer Settings");

        await client.sendMenu(chatId, menuTitle, menuItems);
        return true;
    }

    /**
     * Show test menu with last 15 dialogues (with pagination)
     */
    async showTestMenu(context, offset = 0) {
        const {client, chatId, user} = context;
        const limit = 15;

        try {
            // Get total count for pagination info
            const totalCount = await Dialogue.count({
                where: {
                    botId: context.bot.id
                }
            });

            // Get dialogues for current page
            const dialogues = await Dialogue.findAll({
                where: {
                    botId: context.bot.id
                },
                include: [{
                    model: ExternalUser,
                    attributes: ['id', 'externalId', 'firstName', 'lastName', 'username']
                }],
                order: [['lastMessageDate', 'DESC']],
                limit: limit,
                offset: offset
            });

            if (dialogues.length === 0 && offset === 0) {
                await client.sendMessage(chatId, await l(user, "No dialogues found for analysis."));
                return true;
            }

            // Create inline keyboard with dialogue buttons
            const inlineKeyboard = dialogues.map(dialogue => {
                const dialogueUser = dialogue.ExternalUser;
                if (!dialogueUser) {
                    return [];
                }
                const displayName = dialogueUser.username || dialogueUser.firstName || dialogueUser.externalId;
                const lastMessageDate = dialogue.lastMessageDate ?
                    new Date(dialogue.lastMessageDate).toLocaleDateString() :
                    'No messages';
                const messageCount = dialogue.messageCount || 0;

                const buttonText = `${displayName} - ${lastMessageDate} (${messageCount} msgs)`;

                return [{
                    text: buttonText,
                    callback_data: `d_a_a_d_${dialogue.id}`
                }];
            });

            // Add pagination navigation buttons
            const navigationButtons = [];

            // Previous button
            if (offset > 0) {
                const prevOffset = Math.max(0, offset - limit);
                navigationButtons.push({
                    text: '⬅️ Previous',
                    callback_data: `d_a_prev_page_${prevOffset}`
                });
            }

            // Next button
            if (offset + limit < totalCount) {
                const nextOffset = offset + limit;
                navigationButtons.push({
                    text: 'Next ➡️',
                    callback_data: `d_a_next_page_${nextOffset}`
                });
            }

            if (navigationButtons.length > 0) {
                inlineKeyboard.push(navigationButtons);
            }

            // Create page info message
            const currentPage = Math.floor(offset / limit) + 1;
            const totalPages = Math.ceil(totalCount / limit);
            const pageInfo = totalPages > 1 ? ` (Page ${currentPage}/${totalPages})` : '';

            const message = await l(user, `Select a dialogue to analyze:${pageInfo}`);
            await client.sendMessage(chatId, message, {
                reply_markup: {
                    inline_keyboard: inlineKeyboard
                }
            });

            return true;
        } catch (error) {
            log.error('Error showing test menu', {
                error: error.message,
                botId: context.bot.id,
                offset: offset
            }, 'showTestMenu', 'DialogAnalyzerTool');

            await client.sendMessage(chatId, await l(user, `Error loading dialogues: ${error.message}`));
            return true;
        }
    }

    /**
     * Show analyzer status
     */
    async showStatus(context) {
        const {client, chatId, user, botStorage} = context;

        const config = await this.getConfig(botStorage);

        let message = `📊 <b>${await l(user, "Dialog Analyzer Status")}</b>\n\n`;
        message += `${await l(user, "Status")}: ${config.enabled ? '✅ Enabled' : '❌ Disabled'}\n`;
        message += `${await l(user, "Model")}: ${config.model}\n`;

        if (config.inactivityTimeout === -1) {
            message += `${await l(user, "Automated Analysis")}: ❌ Disabled\n`;
        } else {
            message += `${await l(user, "Timeout")}: ${config.inactivityTimeout / 60} ${await l(user, "minutes")}\n`;
        }

        message += `${await l(user, "Re-analysis")}: ${config.reanalysisEnabled ? '✅ Enabled' : '❌ Disabled'}\n`;
        message += `${await l(user, "Integration")}: ${config.integrationType || 'Not configured'}\n`;

        if (config.lastRun) {
            const lastRun = new Date(config.lastRun);
            message += `${await l(user, "Last run")}: ${lastRun.toLocaleString()}\n`;
        }

        await client.sendMessage(chatId, message, {parse_mode: 'HTML'});
        return true;
    }

    /**
     * Toggle analyzer enabled/disabled
     */
    async toggleAnalyzer(context) {
        const {client, chatId, user, botStorage} = context;

        const config = await this.getConfig(botStorage);
        config.enabled = !config.enabled;
        await botStorage.set('dialogAnalyzer.config', config);

        const status = config.enabled ? 'enabled' : 'disabled';
        await client.sendMessage(chatId, await l(user, `Dialog analyzer ${status}`));

        return true;
    }

    /**
     * Get default configuration
     */
    getDefaultConfig() {
        return {
            enabled: false,
            prompt: "Analyze this dialogue and provide a summary of the conversation, key points discussed, customer needs, and any action items or follow-ups required.",
            model: "gpt-4o",
            temperature: 0.2,
            inactivityTimeout: 1800, // 30 minutes in seconds
            reanalysisEnabled: false,
            integrationType: null,
            integrationConfig: {},
            lastRun: null
        };
    }

    /**
     * Get configuration from storage
     */
    async getConfig(botStorage) {
        const defaultConfig = this.getDefaultConfig();
        const config = await botStorage.get('dialogAnalyzer.config', defaultConfig);
        return {...defaultConfig, ...config};
    }

    /**
     * Find inactive dialogues that need analysis
     */
    async findInactiveDialogues(botId, inactivityTimeoutSeconds) {
        const cutoffTime = new Date(Date.now() - (inactivityTimeoutSeconds * 1000));

        const dialogues = await Dialogue.findAll({
            where: {
                botId: botId,
                status: 'active',
                lastMessageDate: {
                    [Op.lt]: cutoffTime
                }
            },
            include: [{
                model: ExternalUser,
                attributes: ['id', 'externalId', 'firstName', 'lastName', 'username']
            }],
            order: [['lastMessageDate', 'ASC']],
            limit: 50 // Process max 50 dialogues per run
        });

        // Filter dialogues that haven't been analyzed yet or need re-analysis
        const filteredDialogues = [];

        for (const dialogue of dialogues) {
            // Check if dialogue was already analyzed
            const existingAnalysis = await DialogueAnalysis.findOne({
                where: {
                    dialogueId: dialogue.id
                },
                order: [['createdAt', 'DESC']]
            });

            // Check if dialogue needs analysis
            if (!existingAnalysis) {
                // Never analyzed
                filteredDialogues.push(dialogue);
            } else {
                // Check if re-analysis is needed (new messages since last analysis)
                const lastAnalyzedMessageId = existingAnalysis.lastMessageId;
                const latestMessage = await DialogueMessage.findOne({
                    where: {
                        dialogId: dialogue.id
                    },
                    order: [['createdAt', 'DESC']],
                    attributes: ['id']
                });

                if (latestMessage && latestMessage.id !== lastAnalyzedMessageId) {
                    // New messages since last analysis
                    const botStorage = getStorage('Bot', botId);
                    const config = await this.getConfig(botStorage);

                    if (config.reanalysisEnabled) {
                        filteredDialogues.push(dialogue);
                    }
                }
            }
        }

        return filteredDialogues;
    }

    /**
     * Analyze a single dialogue
     */
    async analyzeDialogue(dialogue, config, context) {
        try {
            log.info('Analyzing dialogue', {
                dialogueId: dialogue.id,
                botId: context.bot.id
            }, 'analyzeDialogue', 'DialogAnalyzerTool');

            // Get dialogue messages
            const messages = await DialogueMessage.findAll({
                where: {
                    dialogId: dialogue.id
                },
                order: [['createdAt', 'ASC']],
                attributes: ['message', 'direction', 'createdAt']
            });

            if (messages.length === 0) {
                return {success: false, error: 'No messages found in dialogue'};
            }

            // Format messages for analysis
            const formattedMessages = messages.map(msg => ({
                role: msg.direction === 'user' ? 'User' : 'Assistant',
                content: msg.message,
                timestamp: msg.createdAt
            }));

            // Perform AI analysis
            const analysisResult = await context.oai.ask(
                JSON.stringify(formattedMessages),
                config.prompt,
                config.model,
                config.temperature,
                true // Return usage info
            );

            if (!analysisResult || !analysisResult.response) {
                return {success: false, error: 'Failed to get analysis from AI'};
            }

            // Record token usage
            if (analysisResult.aiTokenCount) {
                await recordTokenUsage(
                    context.bot.user_id,
                    analysisResult.aiTokenCount.total_tokens,
                    analysisResult.aiTokenCount.model,
                    context.bot.id
                );
            }

            // Save analysis result to database
            const analysisData = {
                dialogueId: dialogue.id,
                botId: context.bot.id,
                externalUserId: dialogue.ExternalUser.id,
                analysis: analysisResult.response,
                prompt: config.prompt,
                model: config.model,
                temperature: config.temperature,
                tokenUsage: analysisResult.aiTokenCount,
                lastMessageId: messages[messages.length - 1].id,
                messageCount: messages.length,
                status: 'completed'
            };

            const savedAnalysis = await DialogueAnalysis.create(analysisData);

            // Send to CRM if integration is configured
            if (config.integrationType && config.integrationConfig) {
                const crmResult = await this.sendToCRM(dialogue, savedAnalysis, config, context);
                if (crmResult.success && crmResult.externalId) {
                    // Update analysis record with CRM data
                    await savedAnalysis.update({
                        crmExternalId: crmResult.externalId,
                        crmIntegrationType: config.integrationType,
                        crmSentAt: new Date(),
                        status: 'sent_to_crm'
                    });
                } else if (crmResult.error) {
                    // Update with error
                    await savedAnalysis.update({
                        crmError: crmResult.error,
                        status: 'completed' // Keep as completed even if CRM failed
                    });
                }
            }

            log.info('Dialogue analyzed successfully', {
                dialogueId: dialogue.id,
                botId: context.bot.id,
                tokensUsed: analysisResult.aiTokenCount?.total_tokens
            }, 'analyzeDialogue', 'DialogAnalyzerTool');

            return {success: true, analysis: analysisResult.response};
        } catch (error) {
            log.error('Error analyzing dialogue', {
                dialogueId: dialogue.id,
                error: error.message
            }, 'analyzeDialogue', 'DialogAnalyzerTool');
            return {success: false, error: error.message};
        }
    }

    /**
     * Send analysis result to CRM system
     */
    async sendToCRM(dialogue, analysisRecord, config, context) {
        try {
            if (!this.integrationClasses[config.integrationType]) {
                throw new Error(`Unknown integration type: ${config.integrationType}`);
            }

            const IntegrationClass = this.integrationClasses[config.integrationType];
            const integration = new IntegrationClass(config.integrationConfig);

            // Prepare data for CRM
            const crmData = {
                title: `Dialog Analysis - ${dialogue.ExternalUser.firstName || dialogue.ExternalUser.username || dialogue.ExternalUser.externalId}`,
                analysis: analysisRecord.analysis,
                content: analysisRecord.analysis,
                contact: {
                    id: dialogue.ExternalUser.id,
                    name: `${dialogue.ExternalUser.firstName || ''} ${dialogue.ExternalUser.lastName || ''}`.trim() || dialogue.ExternalUser.username,
                    externalId: dialogue.ExternalUser.externalId
                },
                metadata: {
                    dialogueId: dialogue.id,
                    botId: context.bot.id,
                    analyzedAt: analysisRecord.createdAt,
                    messageCount: analysisRecord.messageCount,
                    model: analysisRecord.model
                }
            };
            return await integration.send(crmData);
            /*
            // Check if this dialogue was already sent to CRM (check for existing CRM external ID)
            if (analysisRecord.crmExternalId) {
                // Update existing record
                return await integration.update(analysisRecord.crmExternalId, crmData);
            } else {
                // Create new record
                return await integration.send(crmData);
            }
             */
        } catch (error) {
            log.error('Error sending to CRM', {
                dialogueId: dialogue.id,
                integrationType: config.integrationType,
                error: error.message
            }, 'sendToCRM', 'DialogAnalyzerTool');
            return {success: false, error: error.message};
        }
    }

    /**
     * Configure analysis prompt
     */
    async configurePrompt(context) {
        const {client, chatId, user, botStorage} = context;

        const config = await this.getConfig(botStorage);

        // Show current prompt and ask if user wants to update
        let message = `📝 <b>${await l(user, "Current Analysis Prompt")}</b>\n\n`;
        message += `<code>${config.prompt}</code>\n\n`;
        message += await l(user, "Do you want to update this prompt?");

        const inlineKeyboard = [
            [{
                text: await l(user, "✅ Yes, Update"),
                callback_data: 'd_a_update_prompt_yes'
            }],
            [{
                text: await l(user, "❌ No, Keep Current"),
                callback_data: 'd_a_update_prompt_no'
            }]
        ];

        await client.sendMessage(chatId, message, {
            parse_mode: 'HTML',
            reply_markup: {
                inline_keyboard: inlineKeyboard
            }
        });

        return true;
    }

    /**
     * Configure AI model
     */
    async configureModel(context) {
        const {client, chatId, user} = context;

        // Get available models from database
        const availableModels = await BotModels.findAll({
            attributes: ['model'],
            order: [['model', 'ASC']]
        });

        if (availableModels.length === 0) {
            await client.sendMessage(chatId, await l(user, "No models available"));
            return true;
        }

        // Create menu items for model selection
        const menuItems = availableModels.map(modelData => ({
            text: modelData.model,
            callback_data: `d_a_set_model_${modelData.model}`
        }));

        const message = await l(user, "Select AI model for analysis:");
        await client.sendMenu(chatId, message, menuItems);

        return true;
    }

    /**
     * Configure inactivity timeout
     */
    async configureTimeout(context) {
        const {client, chatId, user} = context;

        const timeouts = [
            {minutes: 15, seconds: 900, text: await lp(user, "{count} minutes", 15)},
            {minutes: 30, seconds: 1800, text: await lp(user, "{count} minutes", 30)},
            {minutes: 60, seconds: 3600, text: await lp(user, "{count} minutes", 60)},
            {minutes: 120, seconds: 7200, text: await lp(user, "{count} minutes", 120)},
            {text: await l(user, "Disabled"), callback_data: 'd_a_set_timeout_disabled'}
        ];

        const inlineKeyboard = timeouts.map(timeout => [{
            text: timeout.text,
            callback_data: timeout.callback_data || `d_a_set_timeout_${timeout.seconds}`
        }]);

        const message = await l(user, "Select inactivity timeout (or disable automated analysis):");
        await client.sendMessage(chatId, message, {
            reply_markup: {
                inline_keyboard: inlineKeyboard
            }
        });

        return true;
    }

    /**
     * Configure re-analysis settings
     */
    async configureReanalysis(context) {
        const {client, chatId, user, botStorage} = context;

        const config = await this.getConfig(botStorage);

        const inlineKeyboard = [
            [{
                text: config.reanalysisEnabled ? '❌ Disable' : '✅ Enable',
                callback_data: `d_a_toggle_reanalysis`
            }]
        ];

        const status = config.reanalysisEnabled ? 'Enabled' : 'Disabled';
        const message = await l(user, `Re-analysis on new messages: ${status}\n\nClick to toggle:`);

        await client.sendMessage(chatId, message, {
            reply_markup: {
                inline_keyboard: inlineKeyboard
            }
        });

        return true;
    }

    /**
     * Configure CRM integration
     */
    async configureIntegration(context) {
        const {client, chatId, user} = context;

        const integrations = [
            {type: 'bitrix24', name: 'Bitrix24'},
            {type: 'amocrm', name: 'AmoCRM'},
            {type: 'rest_api', name: 'REST API'},
            {type: 'none', name: 'Disable Integration'}
        ];

        const inlineKeyboard = integrations.map(integration => [{
            text: integration.name,
            callback_data: `d_a_set_integration_${integration.type}`
        }]);

        const message = await l(user, "Select CRM integration type:");
        await client.sendMessage(chatId, message, {
            reply_markup: {
                inline_keyboard: inlineKeyboard
            }
        });

        return true;
    }

    /**
     * Test analyze a specific dialogue
     */
    async testAnalyzeDialogue(dialogueId, context, sendToCrm = false) {
        try {
            const {client, chatId, user, botStorage} = context;

            // Find dialogue by ID
            const dialogue = await Dialogue.findOne({
                where: {
                    id: dialogueId,
                    botId: context.bot.id
                },
                include: [{
                    model: ExternalUser,
                    attributes: ['id', 'externalId', 'firstName', 'lastName', 'username']
                }]
            });

            if (!dialogue) {
                await client.sendMessage(chatId, await l(user, "Dialogue not found"));
                return true;
            }

            const config = await this.getConfig(botStorage);

            // Get dialogue messages for message count
            const messages = await DialogueMessage.findAll({
                where: {
                    dialogId: dialogue.id
                },
                order: [['createdAt', 'ASC']],
                attributes: ['message', 'direction', 'createdAt']
            });

            // Perform analysis
            await client.sendMessage(chatId, await l(user, "Analyzing dialogue..."));

            const result = await this.analyzeDialogue(dialogue, config, context);

            if (!result.success) {
                await client.sendMessage(chatId, await l(user, `Analysis failed: ${result.error}`));
                return true;
            }

            // Send result as file
            const fileName = `dialogue_analysis_${dialogue.id}.txt`;
            const filePath = path.join('/tmp', fileName);

            const fileContent = `Dialogue Analysis Report
======================

Dialogue ID: ${dialogue.id}
User: ${dialogue.ExternalUser.firstName || dialogue.ExternalUser.username || dialogue.ExternalUser.externalId}
Analyzed at: ${new Date().toISOString()}

Analysis Result:
${result.analysis}
`;

            fs.writeFileSync(filePath, fileContent);

            await client.sendDocument(chatId, filePath, {
                caption: await l(user, "Analysis completed successfully")
            });

            // Clean up temp file
            fs.unlinkSync(filePath);

            // Send to CRM if requested
            if (sendToCrm && config.integrationType) {
                // Create temporary analysis record for CRM sending
                const tempAnalysisRecord = {
                    analysis: result.analysis,
                    createdAt: new Date(),
                    messageCount: messages.length,
                    model: config.model,
                    crmExternalId: null
                };

                const crmResult = await this.sendToCRM(dialogue, tempAnalysisRecord, config, context);

                if (crmResult.success) {
                    await client.sendMessage(chatId, await l(user, "Analysis sent to CRM successfully"));
                } else {
                    await client.sendMessage(chatId, await l(user, `Failed to send to CRM: ${crmResult.error}`));
                }
            }

            return true;
        } catch (error) {
            log.error('Error in test analysis', {
                error: error.message,
                dialogueId
            }, 'testAnalyzeDialogue', 'DialogAnalyzerTool');

            await context.client.sendMessage(context.chatId, await l(context.user, `Error: ${error.message}`));
            return true;
        }
    }

    /**
     * Start integration configuration process
     */
    async startIntegrationConfig(integrationType, context) {
        const {client, chatId, user} = context;
        const userStorage = getStorage('ExternalUser', user.id);

        if (!this.integrationClasses[integrationType]) {
            await client.sendMessage(chatId, await l(user, "Unknown integration type"));
            return;
        }

        const IntegrationClass = this.integrationClasses[integrationType];
        const integration = new IntegrationClass({});
        const schema = integration.getConfigSchema();

        // Start configuration process
        const configState = {
            integrationType,
            schema,
            currentField: null,
            config: {},
            fields: Object.keys(schema)
        };

        await userStorage.set('dialogAnalyzer.waitingForIntegrationConfig', configState);
        await this.askForNextConfigField(configState, context);
    }

    /**
     * Ask for next configuration field
     */
    async askForNextConfigField(configState, context) {
        const {client, chatId, user} = context;

        // Find next required field
        const nextField = configState.fields.find(field =>
            configState.schema[field].required && !configState.config[field]
        );

        if (!nextField) {
            // All required fields filled, save configuration
            await this.saveIntegrationConfig(configState, context);
            return;
        }

        configState.currentField = nextField;
        const fieldSchema = configState.schema[nextField];

        let message = `Please enter ${nextField}:\n`;
        if (fieldSchema.description) {
            message += `${fieldSchema.description}\n`;
        }
        if (fieldSchema.default) {
            message += `Default: ${fieldSchema.default}\n`;
        }

        await client.sendMessage(chatId, message);

        const userStorage = getStorage('ExternalUser', user.id);
        await userStorage.set('dialogAnalyzer.waitingForIntegrationConfig', configState);
    }

    /**
     * Handle integration configuration input
     */
    async handleIntegrationConfigInput(configState, input, context) {
        const {client, chatId, user} = context;
        const userStorage = getStorage('ExternalUser', user.id);

        const currentField = configState.currentField;
        const fieldSchema = configState.schema[currentField];

        // Validate input
        let value = input.trim();
        if (fieldSchema.type === 'object') {
            try {
                value = JSON.parse(value);
            } catch (error) {
                await client.sendMessage(chatId, await l(user, "Invalid JSON format. Please try again:"));
                return;
            }
        }

        // Save field value
        configState.config[currentField] = value;

        // Ask for next field
        await this.askForNextConfigField(configState, context);
    }

    /**
     * Save integration configuration
     */
    async saveIntegrationConfig(configState, context) {
        const {client, chatId, user, botStorage} = context;
        const userStorage = getStorage('ExternalUser', user.id);

        try {
            // Validate configuration
            const IntegrationClass = this.integrationClasses[configState.integrationType];
            const integration = new IntegrationClass(configState.config);
            const validation = integration.validateConfig(configState.config);

            if (!validation.valid) {
                await client.sendMessage(chatId, await l(user, `Configuration errors:\n${validation.errors.join('\n')}`));
                await userStorage.del('dialogAnalyzer.waitingForIntegrationConfig');
                return;
            }

            // Test connection
            await client.sendMessage(chatId, await l(user, "Testing connection..."));
            const connectionTest = await integration.testConnection();

            if (!connectionTest) {
                await client.sendMessage(chatId, await l(user, "Connection test failed. Please check your configuration."));
                await userStorage.del('dialogAnalyzer.waitingForIntegrationConfig');
                return;
            }

            // Save configuration
            const config = await this.getConfig(botStorage);
            config.integrationType = configState.integrationType;
            config.integrationConfig = configState.config;
            await botStorage.set('dialogAnalyzer.config', config);

            await client.sendMessage(chatId, await l(user, `${configState.integrationType} integration configured successfully!`));
            await userStorage.del('dialogAnalyzer.waitingForIntegrationConfig');

        } catch (error) {
            log.error('Error saving integration config', {
                error: error.message,
                integrationType: configState.integrationType
            }, 'saveIntegrationConfig', 'DialogAnalyzerTool');

            await client.sendMessage(chatId, await l(user, `Error: ${error.message}`));
            await userStorage.del('dialogAnalyzer.waitingForIntegrationConfig');
        }
    }
}

export default DialogAnalyzerTool;

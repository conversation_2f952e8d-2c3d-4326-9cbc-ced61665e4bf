import {ToolInterface} from './base.js';
import axios from 'axios';
import crypto from 'crypto';
import {log} from '../library/log.js';
import {getStorage} from "../library/storage.js";

class AmoCrmTool extends ToolInterface {
    getDefinition() {
        return {}; //no need to use it in openAI
    }

    async execute(args) {
        try {
            const userId = this.context.user.id;

            // Validate required parameters based on action
            if (args.action === "addMessage" && (!args.message || !args.direction)) {
                return "Error: message and direction are required for addMessage action";
            }

            switch (args.action) {
                case "getContact":
                    const contactId = await this.getOrCreateContact(userId);
                    return contactId ? `Contact found with ID: ${contactId}` : "Failed to retrieve contact";

                case "createContact":
                    const newContactId = await this.createContact(userId);
                    return newContactId ? `Contact created with ID: ${newContactId}` : "Failed to create contact";

                case "addMessage":
                    const result = await this.addMessage(userId, args.message, args.direction);
                    return result ? "Message sent to AmoCRM" : "Failed to send message to AmoCRM";

                default:
                    return "Unknown action";
            }
        } catch (e) {
            log.error('amo_crm', {
                msg: e.message
            }, 'execute', 'AmoCrmTool');
            return `Error: ${e.message}`;
        }
    }

    getRegisteredEvents() {
        return ['message_received', 'answer_received'];
    }

    async handleEvent(eventName, eventData, context) {
        try {
            const botId = context.bot.id;
            const chatId = context.chatId;
            const user = context.user;

            // Check if AmoCRM integration is enabled for this bot
            const botStorage = context.botStorage || getStorage('Bot', botId);
            const amoEnabled = await botStorage.get('amo.enabled', false);

            if (!amoEnabled) {
                return false;
            }

            if (eventName === 'message_received') {
                const message = eventData.text || '';
                if (message && message.trim().length > 0) {
                    this.addMessage(user.id, message, 'in').then().catch((e) => {
                        log.error('amo_crm', {
                            msg: e.message,
                            eventName: eventName,
                            userId: user.id
                        }, 'handleEvent', 'AmoCrmTool');
                    });
                }
                return false; // Continue processing
            }

            if (eventName === 'answer_received') {
                const message = eventData.answer?.message || '';
                if (message && message.trim().length > 0) {
                    this.addMessage(user.id, message, 'out').then().catch((e) => {
                        log.error('amo_crm', {
                            msg: e.message,
                            eventName: eventName,
                            userId: user.id
                        }, 'handleEvent', 'AmoCrmTool');
                    });
                }
                return false; // Continue processing
            }

            return false;
        } catch (error) {
            log.error('amo_crm event handling failed', {
                error: error.message,
                eventName: eventName
            }, 'handleEvent', 'AmoCrmTool');
            return false;
        }
    }

    // Helper methods
    async getConfig() {
        const globalStorage = getStorage('GlobalStorage', 'amo');
        const globalConfig = await globalStorage.get('config', {});
        const botStorage = this.context.botStorage || getStorage('Bot', this.context.bot.id);
        const config = await botStorage.get('amo.config', {});

        // Default config structure
        return {
            domain: config.domain || globalConfig.domain || '',
            token: config.token || globalConfig.token || '',
            owner: config.owner || globalConfig.owner || 0,
            ref_id: config.ref_id || globalConfig.ref_id || '',
            chat: {
                id: config.chat?.id || globalConfig?.chat?.id || '',
                secret_key: config.chat?.secret_key || globalConfig?.chat?.secret_key || '',
                amojo_id: config.chat?.amojo_id || globalConfig?.chat?.amojo_id || '',
                scope_id: config.chat?.scope_id || globalConfig?.chat?.scope_id || ''
            },
            fields: {
                telegramID: config.fields?.telegramID || globalConfig?.fields?.telegramID || 0,
                telegram: config.fields?.telegram || globalConfig?.fields?.telegram || 0,
                botID: config.fields?.botID || globalConfig?.fields?.botID || 0,
                botName: config.fields?.botName || globalConfig?.fields?.botName || 0,
                ownerID: config.fields?.ownerID || globalConfig?.fields?.ownerID || 0
            }
        };
    }

    async updateConfig(config) {
        const botStorage = this.context.botStorage || getStorage('Bot', this.context.bot.id);
        return await botStorage.set('amo.config', config);
    }

    async getOrCreateContact(clientId) {
        try {
            const botId = this.context.bot.id;
            log.info('crm/getOrCreateContact', {botId: botId, clientId: clientId}, 'getOrCreateContact', 'AmoCrmTool');

            let contactId = await this.getContact(clientId);
            if (!contactId) {
                log.debug('crm/getOrCreateContact', {
                    msg: 'Contact not found',
                    botId: botId,
                    clientId: clientId
                }, 'getOrCreateContact', 'AmoCrmTool');
                contactId = await this.createContact(clientId);
            }
            return contactId;
        } catch (e) {
            log.error('crm/getOrCreateContact', {
                msg: e.message,
                clientId: clientId
            }, 'getOrCreateContact', 'AmoCrmTool');
            return false;
        }
    }

    async getContact(clientId) {
        try {
            const userStorage = getStorage('ExternalUser', clientId);
            const contactData = await userStorage.get('amo.contact', null);

            if (!contactData || !contactData.crmContactId) {
                return false;
            }
            return contactData.crmContactId;
        } catch (e) {
            log.error('amo/getContact', {msg: e.message, clientId: clientId}, 'getContact', 'AmoCrmTool');
            return false;
        }
    }

    async createContact(userId) {
        try {
            log.info('amo/createContact', {userId: userId}, 'createContact', 'AmoCrmTool');
            const config = await this.getConfig();
            const bot = this.context.bot;

            // Get user profile information
            const user = this.context.user;
            if (!user) {
                log.error('amo/createContact', {msg: 'Invalid user', userId: userId}, 'createContact', 'AmoCrmTool');
                return false;
            }

            // Process username
            let name = user.firstName || 'noName';
            name = name.replace(/[^a-zA-Zа-яА-Я]/g, '');
            //name = `${name}`;
            if (name.length < 2) {
                name = 'noName';
            }
            if (name.length > 10) {
                name = name.substring(0, 10);
            }

            // Process lastname
            let lastname = user.lastName || 'noLastname';
            lastname = lastname.replace(/[^a-zA-Zа-яА-Я]/g, '');
            if (lastname.length < 2) {
                lastname = 'noLastname';
            }
            if (lastname.length > 10) {
                lastname = lastname.substring(0, 10);
            }

            // Create contact object
            const contactObj = [{
                first_name: name,
                last_name: lastname,
                responsible_user_id: parseInt(config.owner),
                "custom_fields_values": [
                    {
                        "field_id": config.fields.telegramID,
                        "values": [
                            {
                                "value": parseInt(user.externalId) || 0
                            }
                        ]
                    },
                    {
                        "field_id": config.fields.telegram,
                        "values": [
                            {
                                "value": user.username || ''
                            }
                        ]
                    },
                    /*
                    {
                        "field_id": config.fields.botID,
                        "values": [
                            {
                                "value": botId.toString()
                            }
                        ]
                    },

                     */
                    {
                        "field_id": config.fields.botName,
                        "values": [
                            {
                                "value": bot.title || ''
                            }
                        ]
                    },
                    /*
                    {
                        "field_id": config.fields.ownerID,
                        "values": [
                            {
                                "value": bot.user_id.toString() || '0'
                            }
                        ]
                    }

                     */
                ]
            }];

            // Call AmoCRM API
            const result = await this.call(contactObj, 'contacts');
            if (!result || !result._embedded || !result._embedded.contacts || !result._embedded.contacts[0]) {
                throw new Error('Failed to create contact: ' + JSON.stringify(result));
            }

            const contactId = result._embedded.contacts[0].id;

            // Store contact ID in user storage
            const userStorage = getStorage('ExternalUser', userId);
            await userStorage.set('amo.contact', {crmContactId: contactId});

            return contactId;
        } catch (e) {
            log.error('amo/createContact', {msg: e.message, userId: userId}, 'createContact', 'AmoCrmTool');
            return false;
        }
    }

    async getChat(contactId) {
        try {
            const userStorage = getStorage('ExternalUser', contactId);
            let contactData = await userStorage.get('amo.contact', {});

            if (!contactData.crmContactId) {
                throw new Error('Invalid contact data');
            }

            if (contactData.chat) {
                return contactData.chat;
            }

            let scopeId = await this.getScopeId();
            if (!scopeId) {
                throw new Error('Invalid scope ID');
            }

            // Create chat
            const path = `/v2/origin/custom/${scopeId}/chats`;
            const user = this.context.user;

            const body = {
                conversation_id: `amo-${this.context.bot.id}-${contactId}`,
                user: {
                    id: `${contactId}`,
                    name: `${user.firstName || 'no name'} ${user.lastName || ''} ${user.username || ''}`,
                },
                profile: {}
            };

            const response = await this.chatCall(body, path, 'POST');
            if (!response || !response.id) {
                throw new Error('Error creating chat');
            }

            // Update user storage
            contactData.chat = response.id;
            contactData.chatData = response;
            await userStorage.set('amo.contact', contactData);

            // Link chat with contact
            const link = await this.call([{
                contact_id: contactData.crmContactId,
                chat_id: contactData.chat
            }], `contacts/chats`);

            if (!link) {
                throw new Error('Error linking chat');
            }

            return response.id;
        } catch (e) {
            log.error('amo/getChat', {msg: e.message, contactId: contactId}, 'getChat', 'AmoCrmTool');
            return null;
        }
    }

    async addMessage(contactId, message, direction) {
        try {
            log.info('amo/addMessage', {
                contactId: contactId,
                message: message,
                direction: direction
            }, 'addMessage', 'AmoCrmTool');

            // Ensure contact exists
            await this.getOrCreateContact(contactId);

            const scopeId = await this.getScopeId();
            if (!scopeId) {
                throw new Error('Invalid scope ID');
            }

            const chat = await this.getChat(contactId);
            if (!chat) {
                throw new Error('Invalid chat');
            }

            const botId = this.context.bot.id;
            const msec = new Date().getTime();
            const now = Math.floor(msec / 1000);

            // Build message payload
            let body = {payload: {}};
            body.payload.timestamp = now;
            body.payload.msec_timestamp = msec;
            body.event_type = 'new_message';
            body.payload.conversation_id = `amo-${botId}-${contactId}`;
            body.payload.silent = true;
            body.payload.msgid = `${msec.toString()}`;
            body.payload.message = {
                type: 'text',
                text: message
            };

            // Set sender/receiver based on direction
            const userStorage = getStorage('ExternalUser', contactId);
            const contactData = await userStorage.get('amo.contact', {});

            if (direction === 'in') {
                // From user to bot
                body.payload.sender = {
                    id: contactId.toString(),
                    name: 'User',
                    ref_id: contactData.chatData?.user?.id || contactId.toString()
                };
            } else if (direction === 'out') {
                const config = await this.getConfig();
                // From bot to user
                body.payload.receiver = {
                    id: contactId.toString(),
                    name: 'User',
                    ref_id: contactData.chatData?.user?.id || contactId.toString()
                };
                body.payload.sender = {
                    ref_id: config.ref_id
                };
            }

            // Send message to AmoCRM
            const path = `/v2/origin/custom/${scopeId}`;
            const response = await this.chatCall(body, path, 'POST');

            if (!response) {
                throw new Error('Error sending message');
            }

            return true;
        } catch (e) {
            log.error('amo/addMessage', {
                msg: e.message,
                contactId: contactId,
                message: message,
                direction: direction
            }, 'addMessage', 'AmoCrmTool');
            return false;
        }
    }

    async getScopeId() {
        try {
            const config = await this.getConfig();
            if (!this.chatEnabled(config)) {
                return false;
            }

            if (config.chat.scope_id) {
                return config.chat.scope_id;
            }

            const amojoId = await this.getAmojoId();
            const path = `/v2/origin/custom/${config.chat.id}/connect`;

            const body = {
                account_id: amojoId,
                title: 'Chat',
                hook_api_version: 'v2',
            };

            const response = await this.chatCall(body, path, 'POST');

            if (!response) {
                log.error('amo/getScopeId', {
                    msg: 'Error getting scope id',
                    response: response,
                    body: body,
                    path: path
                }, 'getScopeId', 'AmoCrmTool');
                return false;
            }

            // Update config with new scope ID
            config.chat.scope_id = response.scope_id;
            await this.updateConfig(config);

            return config.chat.scope_id;
        } catch (e) {
            log.error('amo/getScopeId', {msg: e.message}, 'getScopeId', 'AmoCrmTool');
            return false;
        }
    }

    async getAmojoId() {
        try {
            const config = await this.getConfig();
            if (!this.chatEnabled(config)) {
                return false;
            }

            if (config.chat.amojo_id) {
                return config.chat.amojo_id;
            }

            const path = `account?with=amojo_id`;
            const response = await this.call({}, path, 'GET');

            if (!response || !response.amojo_id) {
                log.error('amo/getAmojoId', {
                    msg: 'Error getting amojo id',
                    response: response
                }, 'getAmojoId', 'AmoCrmTool');
                return false;
            }

            // Update config with new amojo ID
            config.chat.amojo_id = response.amojo_id;
            await this.updateConfig(config);

            return config.chat.amojo_id;
        } catch (e) {
            log.error('amo/getAmojoId', {msg: e.message}, 'getAmojoId', 'AmoCrmTool');
            return false;
        }
    }

    chatEnabled(config = null) {
        if (!config) {
            return false;
        }

        if (typeof config.chat !== 'undefined') {
            if (config.chat.id && config.chat.secret_key) {
                return true;
            }
        }
        return false;
    }

    async chatCall(body, path, method = 'POST') {
        try {
            const config = await this.getConfig();
            const contentType = 'application/json';
            const date = new Date().toUTCString();
            const url = `https://amojo.amocrm.ru${path}`;
            const requestBody = JSON.stringify(body);
            const checkSum = crypto.createHash('md5').update(requestBody).digest('hex');

            const str = [
                method,
                checkSum,
                contentType,
                date,
                path,
            ].join('\n');

            const signature = crypto.createHmac('sha1', config.chat.secret_key).update(str).digest('hex');

            const headers = {
                'Date': date,
                'Content-Type': contentType,
                'Content-MD5': checkSum.toLowerCase(),
                'X-Signature': signature.toLowerCase(),
            };

            log.info('amo/chatCall', {
                headers: headers,
                body: body,
                url: url,
                checkSum: checkSum,
                signature: signature
            }, 'chatCall', 'AmoCrmTool');

            const response = await axios.post(url, requestBody, {headers: headers, method: 'POST'});

            if (!response || !response.data) {
                log.error('amo/chatCall', {
                    msg: 'Invalid response',
                    response: response,
                    headers: headers,
                    body: body,
                    url: url,
                    checkSum: checkSum,
                    signature: signature
                }, 'chatCall', 'AmoCrmTool');
                return false;
            }

            log.debug('amo/chatCall', {response: response.data}, 'chatCall', 'AmoCrmTool');
            return response.data;
        } catch (e) {
            log.error('amo/chatCall', {
                msg: e.message,
                body: body,
                path: path,
                response: e.response?.data || {}
            }, 'chatCall', 'AmoCrmTool');
            return false;
        }
    }

    async call(request, url, method = 'POST') {
        try {
            const config = await this.getConfig();
            log.info('amo/call', {
                request: request,
                url: `https://${config.domain}/api/v4/${url}`,
                method: method
            }, 'call', 'AmoCrmTool');

            const requestConfig = {
                method: method,
                maxBodyLength: Infinity,
                url: `https://${config.domain}/api/v4/${url}`,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${config.token}`
                },
                data: request
            };

            const response = await axios.request(requestConfig);

            if (!response.data) {
                throw new Error('Invalid response: ' + JSON.stringify(response.data));
            }

            log.info('amo/call', {request: request, response: response.data}, 'call', 'AmoCrmTool');
            return response.data;
        } catch (e) {
            log.error('amo/call', {
                msg: e.message,
                request: request,
                response: e.response ? e.response.data : null
            }, 'call', 'AmoCrmTool');
            return false;
        }
    }
}

export default AmoCrmTool;